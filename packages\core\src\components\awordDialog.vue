<template>
    <!-- 种植完成弹窗 -->
    <!-- <div class="box" v-on:touchstart="touchstart" v-on:touchend="touchend"> -->
    <div class="box" @click="useDialog().getInstance(uuid).state.opts.maskClose ? closeDlg() : null">
        <!-- <img class="hhaa ll1" v-if="step == 0" :src="$imgs['j2.png']" alt="">
      <img class="hhaa rr1" v-if="step == stepNum-1" :src="$imgs['j2.png']" alt=""> -->
        <img class="hhaa ll" @click.stop="iknew" v-if="step > 0" :src="$imgs['j1.png']" alt="" />
        <img class="hhaa rr" @click.stop="iknow" v-if="step < stepNum - 1" :src="$imgs['j1.png']" alt="" />

        <p class="tip">{{ state.dialog.获得成长奖励 }}</p>
        <p class="tipNum">{{ `(${step + 1}/${stepNum})` }}</p>

        <div class="aword2" v-if="list[step] == 'int'">
            <div v-if="useUserStore().amm_vip || ammVip" class="bubble">
                {{ state.link会员.加赠了 }}
                <span>50%</span>
            </div>
            <p v-else>+{{ integral_value }}{{ state.dialog.积分 }}</p>
            <img :src="$imgs['coin.png']" alt="" />
            <span v-if="useUserStore().amm_vip || ammVip" class="vip-tag">{{ state.link会员.Link着数会员 }}</span>
            <div v-if="useUserStore().amm_vip || ammVip" class="points-box">
                <p class="old-points">+{{ Math.round(integral_value / 1.5) }}{{ state.dialog.积分 }}</p>
                <img :src="$imgs['dialog/linkMember-up.png']" />
                <p class="new-points">+{{ integral_value }}{{ state.dialog.积分 }}</p>
            </div>
            <!-- 积分风控提示 -->
            <div class="Tips mb-82" v-if="integral_Status === 0">
                <div class="Title">
                    <span class="Tips-icon">i</span>
                    <span class="Tips-title">{{ state.dialog.注意 }}</span>
                </div>
                <div class="content mt-02">
                    {{ state.dialog.积分奖励发放失败 }}
                </div>
            </div>
            <div class="Tips Tips-size mb-82" :class="`Tips-size_${lang}`" v-if="integral_Status === -1">
                <div class="Title">
                    <span class="Tips-icon">i</span>
                    <span class="Tips-title">{{ state.dialog.注意 }}</span>
                </div>
                <div class="content">
                    {{ state.dialog.阁下的设备已超出 }}
                    <span class="link" @click="router.push({ path: '/rule', query: { hideNavigationBar: true } })">
                        {{ state.dialog['「MyGarden种植奖励计划条款及细则」'] }}
                    </span>
                </div>
            </div>
            <p class="bb" v-if="integral_Status === 1">
                {{ state.dialog.奖励积分已存入 }}
            </p>
        </div>

        <div v-if="list[step] == 'prop'" class="aword">
            <div class="aword1">
                <!-- 奖励弹窗 -->
                <img :src="$imgs['awordbg.png']" alt="" />
                <p class="rarity">{{ state.dialog.稀有度 }}</p>
                <div class="star">
                    <div v-for="i in Number(state.propsReward[propsItem.code].propValue)" :key="i"></div>
                </div>
                <div class="pic">
                    <img
                        :src="$imgs[`props/${state.propsReward[propsItem.code].propType}/${propsItem.code}.png`]"
                        alt=""
                    />
                </div>
                <p class="name">{{ state.propsReward[propsItem.code].propName }}</p>
            </div>
            <p class="bb pb-46">
                {{ state.dialog.奖励已经放进背包了哦 }}
            </p>
        </div>

        <div v-if="list[step] == 'com'" class="aword aword3">
            <!-- <p class="congrate">{{state.dialog.恭喜获得x种植成就.replace('{x}', state.plant[treeCode].plantName)}}  </p> -->
            <p class="congrate">{{ state.dialog.恭喜获得x种植成就.replace('{x}', state.plant[treeCode].plantName) }}</p>
            <achieve class="achievement" type="big" :treeCode="treeCode" />
            <!-- TODO NFT -->
            <div
                class="goNFT-btn"
                v-if="useUserStore().isHK == 0 && treeCode !== 'shuishirong' && treeCode !== 'huangzhongmu'"
                @click="
                    router.push({
                        path: '/treeNFT/' + props.tree_id,
                        query: {
                            hideNavigationBar: 'true',
                            treeCode: props.treeCode,
                            treeId: props.tree_id
                        }
                    })
                "
            >
                <span>{{ state.nft.鑄造NFT }}</span>
            </div>
            <div
                class="give-btn"
                v-if="useUserStore().isHK == 0 && treeCode !== 'shuishirong' && treeCode !== 'huangzhongmu'"
                @click="
                    router.push({
                        path: '/friendList',
                        query: {
                            hideNavigationBar: 'true',
                            treeCode
                        }
                    })
                "
            >
                <span>{{ state.flower.贈送好友 }}</span>
            </div>
            <p
                class="give-text"
                v-if="useUserStore().isHK == 0 && treeCode !== 'shuishirong' && treeCode !== 'huangzhongmu'"
            >
                {{ state.flower.注 }}
            </p>
            <p class="fanhui" v-if="step == stepNum - 1">{{ state.dialog.点击屏幕返回 }}</p>
        </div>

        <div v-if="list[step] == 'GIF'" class="aword">
            <!-- <p class="congrate">{{state.dialog.恭喜获得x种植成就.replace('{x}', state.plant[treeCode].plantName)}}  </p> -->
            <gifPropDialog :isInfo="false" :propInstance="Gif" />
            <p class="bb">
                {{ state.dialog.奖励已经放进背包了哦 }}
            </p>
        </div>

        <div @click.stop="iknow" v-if="list[step] != 'prop' && list[step] != 'com'" class="know">
            {{ state.dialog.知道啦 }}
        </div>

        <div v-if="list[step] == 'prop'" class="knowbox">
            <div @click.stop="toPackage" class="know">
                {{ state.dialog.前往背包 }}
            </div>
            <div @click.stop="wearProp" class="know">
                {{ state.dialog.立即使用 }}
            </div>
            <p class="fanhui" v-if="step == stepNum - 1">{{ state.dialog.点击屏幕返回 }}</p>
        </div>
    </div>
</template>

<script setup>
import { useDialog, useLang, useRouter } from 'hook'
import gifPropDialog from './gifPropDialog.vue'
import { defineComponent, onBeforeMount, onMounted, inject, ref, watch, getCurrentInstance } from 'vue'
import achieve from './achieve.vue'
import { useTaskStore, useUserStore } from '@store/index'
const { router } = useRouter()
const props = defineProps({
    awordArr: {
        type: Array,
        required: true
    },
    // 积分值
    integral_value: {
        type: Number,
        required: false
    },
    // 积分的风控状态
    integral_Status: {
        type: Number,
        required: false
    },
    treeCode: {
        type: String,
        required: false
    },
    // 升到 to 级
    to: {
        type: Number,
        required: false,
        default: 0
    },
    tree_id: {
        type: Number || string
    },
    ammVip: {
        type: Boolean,
        required: false,
        default: false
    }
})
console.log(props, '风控状态')

const { state, lang } = useLang()
let emit = defineEmits(['close'])

let step = ref(0)
let stepNum = ref(0)
let type = ref(0)
let list = ref([])
let Gif = ref(null)
let propsItem = ref(null)
let donShake = false

const uuid = inject('uuid')
const closeAword = () => {
    useDialog().getInstance(uuid)?.emit('closeAword')
}

const closeDlg = () => {
    if (donShake) return
    donShake = true
    emit('close')
}

onBeforeMount(() => {
    if (props.integral_value > 0) {
        list.value.push('int')
    }
    if (props.awordArr.length > 1) {
        Gif.value = props.awordArr.filter((item) => {
            return state.propsReward[item.code].propKind == '会员成长'
        })
        Gif.value = Gif.value[0]

        Gif.value = Object.assign({ code: Gif.value.code }, state.propsReward[Gif.value.code])
        list.value.push('GIF')
    }
    propsItem.value = props.awordArr.filter((item) => {
        return state.propsReward[item.code].propKind == '我的花园'
    })
    propsItem.value = propsItem.value[0]
    list.value.push('prop')
    if (props.to == 10) list.value.push('com')
    stepNum.value = list.value.length
    if (list.value.length == 1) {
        useDialog().getInstance(uuid).state.opts.maskClose = true
    }
})

const iknow = () => {
    if (step.value == stepNum.value - 1) {
    } else {
        step.value++
    }
}

const iknew = () => {
    if (step.value == 0) {
    } else {
        step.value--
    }
}

const wearProp = () => {
    useDialog().getInstance(uuid)?.emit('wearProp', props.awordArr[0])
}

const toPackage = () => {
    useDialog().getInstance(uuid)?.emit('toPackage')
}

watch(step, () => {
    if (step.value == stepNum.value - 1) {
        useDialog().getInstance(uuid).state.opts.maskClose = true
    } else {
        useDialog().getInstance(uuid).state.opts.maskClose = false
    }
})

let startX = 0 //开始位置
let endX = 0 //结束触摸的位置
let disX = 0 //移动距离

const touchstart = (ev) => {
    console.log('触摸开始')
    ev = ev || event
    ev.preventDefault()
    if (ev.touches.length == 1) {
        //tounches类数组，等于1时表示此时有只有一只手指在触摸屏幕
        startX = ev.touches[0].clientX // 记录开始位置
    }
}

const touchend = (ev) => {
    console.log('触摸结束')
    ev = ev || event
    ev.preventDefault()
    if (ev.changedTouches.length == 1) {
        let body = document.body.getBoundingClientRect()
        console.log(body.width)
        endX = ev.changedTouches[0].clientX
        disX = endX - startX
        if (disX > 0) {
            if (disX >= body.width / 2) {
                console.log('上一个')
                iknew()
            }
        } else {
            if (disX <= body.width / 2) {
                console.log('下一个')
                iknow()
            }
        }
    }
}
</script>

<style lang="less" scoped>
.box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100vw;
    position: relative;
    // pointer-events: none;
    // background-color: pink;
    .hhaa {
        width: 38px;
        height: 61px;
        position: absolute;
        top: 344px;
        z-index: 2;
        &.ll {
            left: 100px;
        }
        &.rr {
            right: 100px;
            transform: rotate(180deg);
        }
        &.ll1 {
            left: 100px;
            transform: rotate(180deg);
        }
        &.rr1 {
            right: 100px;
        }
    }
    .closeH5 {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
    }
    .tip {
        font-size: 48px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #ffffff;
    }
    .tipNum {
        margin-bottom: 22px;
        font-size: 32px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #ffffff;
    }
    .aword {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        // pointer-events: none;
    }
    .aword1 {
        // margin: 40px 0;

        width: 328px;
        height: 460px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        > img {
            width: 328px;
            height: 460px;
            position: absolute;
            top: 0;
            left: 0;
        }
        .rarity {
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            top: 5px;
            font-size: 26px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            white-space: nowrap;
        }
        .star {
            display: flex;
            position: relative;
            justify-content: center;
            padding-top: 70px;
            padding-bottom: 30px;
            div {
                width: 44px;
                height: 42px;
                background: url('@assets/TT/Icon/star.png') no-repeat;
                background-size: 100% auto;
                margin-right: 10px;
            }
            div:last-child {
                margin-right: 0px;
            }
        }
        .pic {
            position: relative;
            width: 192px;
            height: 192px;
            background: #efefef;
            border: 8px solid #e2e2e2;
            border-radius: 50%;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            // flex-shrink: 0;
            img {
                max-width: 90%;
                max-height: 90%;
            }
        }
        .name {
            position: absolute;
            bottom: 20px;

            left: 50%;
            transform: translate(-50%, 0);
            height: 100px;
            width: 100%;
            // background-color: pink;
            font-size: 32px;
            font-family: PingFang TC-Semibold, PingFang TC;
            font-weight: 600;
            color: #4e5b7e;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
        }
    }
    .aword4 {
        margin-bottom: 52px;
    }
    .aword2 {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 70px;
        > p {
            font-size: 32px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #ffffff;
        }
        > img {
            width: 248px;
        }
        .bubble {
            transform: translateY(20px);
            position: relative;
            background: #fff;
            background: #e9eaeb;
            border-radius: 48px 48px 48px 48px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
            padding: 6px 20px;
            max-width: 240px;
            display: inline-block;
            word-break: break-word;
            font-weight: 500;
            font-size: 28px;
            color: #4e5b7e;
            &::after {
                content: '';
                position: absolute;
                bottom: -15px; /* 箭头高度 20px，紧贴气泡 */
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 15px solid transparent;
                border-right: 15px solid transparent;
                border-top: 20px solid #e9eaeb;
                filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.08));
            }
        }
        .vip-tag {
            margin-top: 20px;
            padding: 10px 16px;
            max-width: 240px;
            display: inline-block;
            word-break: break-word;
            background: linear-gradient(90deg, #9b7efe 0%, #7b5afc 100%);
            border-radius: 48px 48px 48px 48px;
            font-weight: bold;
            font-size: 28px;
            color: #ffffff;
            line-height: 32px;
            text-align: center;
        }
        .points-box {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            .old-points {
                font-weight: 400;
                font-size: 20px;
                color: #ffffff;
                line-height: 28px;
                text-align: center;
                text-decoration-line: line-through;
                text-transform: none;
            }
            img {
                width: 16px;
                height: auto;
                margin: 0 6px;
            }
            .new-points {
                font-weight: 400;
                font-size: 28px;
                color: #ffffff;
                line-height: 28px;
                text-align: center;
            }
        }
    }
    .aword3 {
        background: none;
        margin: 70px 0;
        .congrate {
            font-size: 32px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #ffffff;
            line-height: 32px;
            margin-bottom: 48px;
        }
        .goNFT-btn {
            width: 430px;
            height: 84px;
            background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
            box-shadow: 0px 8px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
            border-radius: 48px 12px 48px 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 84px;
            span {
                font-size: 36px;
                font-family: PingFang SC-Bold, PingFang SC;
                font-weight: bold;
                color: #ffffff;
                line-height: 32px;
                text-shadow: 0px 0px 12px #fbac2e;
            }
        }
        .give-btn {
            width: 430px;
            height: 84px;
            background: linear-gradient(180deg, #56d3eb 0%, #34aadf 100%);
            box-shadow: 0px 8px 0px 2px rgba(56, 120, 185, 1), inset 0px 4px 0px 2px rgba(140, 239, 247, 1);
            border-radius: 48px 12px 48px 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 44px;
            span {
                font-size: 36px;
                font-family: PingFang SC-Bold, PingFang SC;
                font-weight: bold;
                color: #ffffff;
                line-height: 32px;
                text-shadow: 0px 0px 12px #fbac2e;
            }
        }
        .give-text {
            font-size: 28px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #ffffff;
            line-height: 28px;
            margin-top: 46px;
            padding: 0 130px;
            text-align: center;
        }
    }
    .fanhui {
        position: absolute;
        bottom: -140px;
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #ffffff;
        left: 50%;
        transform: translate(-50%, 0);
        white-space: nowrap;
    }
    .bb {
        font-size: 28px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #ffffff;
        padding-bottom: 82px;
        padding-top: 24px;
        text-align: center;
    }
    .knowbox {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        > .know {
            margin-bottom: 40px;
        }
        > .know:nth-child(2) {
            background: linear-gradient(180deg, #56d3eb 0%, #34aadf 100%);
            box-shadow: 0px 8px 0px 2px rgba(56, 120, 185, 1), inset 0px 4px 0px 2px rgba(140, 239, 247, 1);
            margin-bottom: 0px;
        }
    }
    .know {
        min-width: 430px;
        height: 84px;
        background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
        box-shadow: 0px 8px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
        border-radius: 48px 12px 48px 12px;
        font-size: 36px;
        font-family: PingFang SC-Bold, PingFang SC;
        font-weight: bold;
        color: #ffffff;
        line-height: 32px;
        text-shadow: 0px 0px 12px #fbac2e;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .Tips {
        padding: 20px 40px 0 40px;
        margin-top: 86px;
        width: 420px;
        height: 114px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 24px 24px 24px 24px;
        .Title {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .Tips-icon {
            display: block;
            width: 22px;
            height: 22px;
            background: #fd4232;
            font-size: 20px;
            color: white;
            text-align: center;
            line-height: 22px;
            border-radius: 50%;
        }
        .Tips-title {
            font-weight: 600;
            font-size: 24px;
            color: #4e5b7e;
        }
        .content {
            margin-top: 4px;
            font-size: 20px;
            color: #4e5b7e;
        }
        .mt-02 {
            margin-top: 0.16rem;
        }
        .link {
            color: #006cff;
        }
    }
    .Tips-size_tc,
    .Tips-size_sc {
        width: 540px;
        height: 134px;
    }
    .Tips-size_en {
        width: 540px;
        height: 174px;
        line-height: 28px;
    }
    .mt-24 {
        margin-top: 24px;
    }
    .pb-46 {
        padding-bottom: 46px;
    }

    .mb-82 {
        margin-bottom: 82px;
    }
}
</style>
