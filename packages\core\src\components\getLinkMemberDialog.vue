<template>
    <div class="getLinkMemberDialog">
        <div class="title">{{ state.link会员.您暂未获得以下途径有机会获得 }}</div>
        <div class="box">
            <div class="vip">{{ state.link会员.Link着数会员 }}</div>
            <div class="card-name">
                <img :src="$imgs['dialog/linkMember-icon1.png']" />
                <span>30g{{ state.link会员.碳值卡 }}</span>
            </div>
            <p class="desc" v-html="state.link会员.MyGarden生成30g减碳球"></p>
            <p class="tips">{{ state.link会员.于每月1号来到MyGarden才会发放 }}</p>
            <img class="card-img" :src="$imgs[`chatu/碳值30g.png`]" />
        </div>
        <div class="btn purple" @click="buyLinkMemberVip">
            <span>{{ state.link会员.去开通 }}</span>
            <p v-html="state.link会员.开通Link着数会员升级至30g减碳卡"></p>
        </div>
        <div class="btn yellow" @click="close">{{ state.link会员.知道了 }}</div>
        <!-- <img :src="$imgs['dialog/closeIcon.png']" class="close" /> -->
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter, useLang, useEnvConfig } from 'hook'
import { logEventStatistics } from '@via/mylink-sdk'

let emit = defineEmits(['close', 'successUse'])
const env = useEnvConfig()
const { state, lang } = useLang()

const buyLinkMemberVip = () => {
    logEventStatistics('jetsomygarden_clwindowapply')
    emit('close')
    if (env.RUN_ENV == 'develop' || env.RUN_ENV == 'uat') {
        location.href =
            'openurl-modal://https://marketplaceuat.hk.chinamobile.com/marketHK/#/jetsoVip?token=<<cmcchkhsh_ssoToken>>&lang=<<cmcchkhsh_cmplang>>&shopCode=my_garden'
    } else {
        location.href =
            'openurl-modal://https://marketplace.hk.chinamobile.com/marketHK/#/jetsoVip?token=<<cmcchkhsh_ssoToken>>&lang=<<cmcchkhsh_cmplang>>&shopCode=my_garden'
    }
}
const close = () => {
    logEventStatistics('jetsomygarden_clwindowok')
    emit('close')
}
</script>
<style scoped lang="less">
.getLinkMemberDialog {
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 0 100px;
    > .title {
        font-weight: 400;
        font-size: 40px;
        color: #ffffff;
        text-align: center;
        margin-bottom: 62px;
    }
    .box {
        width: 400px;
        padding: 0 20px;
        background: #ffffff;
        border-radius: 72px 24px 72px 24px;
        margin: 0 auto;
        padding-bottom: 48px;
        .vip {
            display: table;
            margin: 0 auto;
            margin-bottom: 22px;
            font-weight: bold;
            font-size: 28px;
            color: #ffffff;
            line-height: 32px;
            text-align: center;
            padding: 4px 16px;
            border-radius: 0px 0px 16px 16px;
            background: linear-gradient(90deg, #9b7efe 0%, #7b5afc 100%);
        }
        .card-name {
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                width: 32px;
                height: 32px;
                margin-right: 4px;
            }
            span {
                display: block;
                font-weight: 500;
                font-size: 32px;
                color: #4e5b7e;
            }
        }
        .desc {
            margin-top: 16px;
            font-weight: 400;
            font-size: 24px;
            color: #4e5b7e;
            text-align: center;
            i {
                font-style: normal;
            }
            span {
                color: #fb7655;
            }
        }
        .tips {
            font-weight: 400;
            font-size: 18px;
            color: #196f20;
            padding: 12px 0 24px;
            text-align: center;
        }
        > .card-img {
            display: block;
            width: 120px;
            height: auto;
            margin: 0 auto;
        }
    }
    .btn {
        width: 430px;
        min-height: 84px;
        border-radius: 48px 12px 48px 12px;
        &.purple {
            margin-top: 34px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: linear-gradient(180deg, #9b7efe 0%, #7b5afc 100%);
            box-shadow: 0px 8px 0px 2px #6844f4, inset 0px 4px 0px 2px #b39cff;
            span {
                display: block;
                font-weight: bold;
                font-size: 32px;
                color: #ffffff;
                line-height: 32px;
                text-shadow: 0px 0px 12px #3a0de8;
                text-align: center;
            }
            p {
                font-weight: bold;
                font-size: 24px;
                color: #ffffff;
                line-height: 32px;
                text-align: center;
                i {
                    color: #9bff9d;
                }
                em,
                i {
                    font-style: normal;
                }
            }
        }
        &.yellow {
            margin-top: 34px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 32px;
            color: #ffffff;
            text-shadow: 0px 0px 12px #af6a0f;
            text-align: center;
            background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
            box-shadow: 0px 8px 0px 2px #fcaf28, inset 0px 4px 0px 2px #fff2b2;
        }
    }
}
</style>
