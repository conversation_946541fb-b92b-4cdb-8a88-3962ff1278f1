<template>
    <!-- 道具展示容器 -->
    <!-- tabIndex:{{ tabIndex }} chooseKind:{{ chooseKind }} kindIndex:{{ kindIndex }} -->
    <div class="propItems">
        <!-- 预留：其他道具分类（暂时注释） -->
        <!-- <div v-if="kindIndex == 2" class="kindIndex">
            <div class="nullBox">
                <img class="null" :src="$imgs['empty.png']" alt="">
                <p class="nullFont">{{state.package_.更多精美道具敬请期待}}</p>
            </div>
        </div> -->

        <!-- mygarden tab（tabIndex == 0） -->
        <div style="height: 100%" v-if="tabIndex == 0">
            <!-- 普通装饰道具展示区域（非标题装饰） -->
            <div v-if="kindIndex == 0 && curTabName !== '标题装饰'" class="kindIndex">
                <!-- 循环展示当前分类下的道具列表 -->
                <div
                    v-for="(item, index) in list[curTabName]"
                    :key="index"
                    class="item"
                    :class="{ item3: !item.propValue }"
                >
                    <!-- 有道具价值的道具展示 -->
                    <template v-if="item.propValue">
                        <!-- 新道具标识 -->
                        <div class="newest" v-if="item.newest">
                            {{ state.联动奖赏.新 }}
                        </div>
                        <!-- 会员道具标签 -->
                        <div class="tips" v-if="item.propKind == '会员成长' && removeTips(item.code)">
                            <p>{{ state.联动奖赏.後付限定 }}</p>
                        </div>
                        <!-- 道具信息图标 -->
                        <img class="info" :src="$imgs['note.png']" alt="" />
                        <!-- 道具名称 -->
                        <div class="name">
                            <p>{{ item.propName }}</p>
                        </div>
                        <!-- 普通道具预览图（非会员成长道具） -->
                        <div class="preview" @click="showProp(item)" v-if="item.propKind != '会员成长'">
                            <img :src="$imgs[`props/${item.propType}/${item.code}.png`]" alt="" v-if="item.propType" />
                            <img :src="$imgs[`props/vip/${item.code}/1.png`]" alt="" v-else />
                        </div>
                        <!-- 会员成长道具预览图（GIF动画） -->
                        <div class="preview" @click="showGifProp(item)" v-else>
                            <img :src="$imgs[`props/vip/${item.code}/1.png`]" alt="" />
                        </div>
                        <!-- 道具稀有度星级显示 -->
                        <div class="value">
                            <img
                                v-for="i in Number(item.rarity ? item.rarity : item.propValue)"
                                :key="i"
                                class="star"
                                src="@imgs/plantSelection/star.png"
                            />
                        </div>
                        <!-- 道具操作按钮 -->
                        <!-- 道具操作按钮（根据道具状态显示不同样式和文字） -->
                        <div
                            class="btn"
                            :class="[
                                !item.owned
                                    ? isGet(item.code)
                                        ? 'go-get'
                                        : 'lock-btn'
                                    : wearTitle.includes(item.propKind) &&
                                      !(item.propType == '标题装饰' && props.chooseKind == 0)
                                    ? item.actived
                                        ? 'on'
                                        : 'off'
                                    : 'off',
                                item.propType === '挂饰' && maxWear < 1 && item.owned ? 'lock-btn' : ''
                            ]"
                            @click="wear(item)"
                        >
                            {{
                                !item.owned
                                    ? isGet(item.code)
                                        ? state.springFestival.去獲取
                                        : state.package_.未获得
                                    : wearTitle.includes(item.propKind) &&
                                      !(item.propType == '标题装饰' && props.chooseKind == 0)
                                    ? item.actived
                                        ? state.package_.取消使用
                                        : state.package_.使用
                                    : state.package_.去查看
                            }}
                        </div>
                        <!-- 未拥有道具的锁定图标 -->
                        <div v-if="!item.owned" class="lock">
                            <img class="lock-img" src="@imgs/package/lock.png" />
                        </div>
                    </template>
                    <!-- 无道具价值的道具展示（会员专属道具） -->
                    <template v-else>
                        <!-- 会员专属标签 -->
                        <div class="tips">
                            <p>{{ state.联动奖赏.後付限定 }}</p>
                        </div>
                        <!-- 新道具标识 -->
                        <div class="newest" v-if="item.newest">
                            {{ state.联动奖赏.新 }}
                        </div>
                        <!-- 道具名称（从角色配置中获取） -->
                        <div class="name">
                            <p>{{ state.package.role[item.code] }}</p>
                        </div>
                        <!-- 会员道具预览图 -->
                        <div class="preview2" @click="showGifProp(item)">
                            <img :src="$imgs[`props/vip/${item.code}/1.png`]" alt="" />
                        </div>
                        <!-- 道具稀有度星级显示 -->
                        <div class="value">
                            <img
                                v-for="i in Number(item.rarity)"
                                :key="i"
                                class="star"
                                src="@imgs/plantSelection/star.png"
                            />
                        </div>
                        <!-- 会员道具操作按钮 -->
                        <div
                            class="btn"
                            :class="!item.owned ? 'lock-btn' : item.actived ? 'on' : 'off'"
                            @click="wearVip(item)"
                        >
                            {{
                                !item.owned
                                    ? state.联动奖赏.未获取
                                    : item.actived
                                    ? state.package_.取消使用
                                    : state.package_.使用
                            }}
                        </div>
                        <div v-if="!item.owned" class="lock">
                            <img class="lock-img" src="@imgs/package/lock.png" />
                        </div>
                    </template>
                </div>
            </div>

            <div v-if="kindIndex == 0 && curTabName == '标题装饰'" class="kindIndex">
                <div
                    v-for="(item, index) in list[curTabName]"
                    :key="index"
                    class="item"
                    :class="{ item3: !item.propValue }"
                >
                    <template v-if="item.propValue">
                        <div class="newest" v-if="item.newest">
                            {{ state.联动奖赏.新 }}
                        </div>
                        <!-- 会员道具标签 -->
                        <div class="tips" v-if="item.propKind == '会员成长' && removeTips(item.code)">
                            <p>{{ state.联动奖赏.後付限定 }}</p>
                        </div>
                        <img class="info" :src="$imgs['note.png']" alt="" />
                        <div class="name">
                            <p>{{ item.propName }}</p>
                        </div>
                        <div class="preview" @click="showProp(item)" v-if="item.propKind != '会员成长'">
                            <img :src="$imgs[`props/${item.propType}/${item.code}.png`]" alt="" v-if="item.propType" />
                            <img :src="$imgs[`props/vip/${item.code}/1.png`]" alt="" v-else />
                        </div>
                        <div class="preview" @click="showGifProp(item)" v-else>
                            <img :src="$imgs[`props/vip/${item.code}/1.png`]" alt="" />
                        </div>
                        <div class="value">
                            <p class="num1" style="bottom: 3px" v-if="item.view_count != 0">
                                {{ state.home.目前数量 }} :
                                <span class="num1-length">{{ item.view_count }} {{ state.nft.件 }}</span>
                            </p>
                            <img
                                v-else
                                v-for="i in Number(item.rarity ? item.rarity : item.propValue)"
                                :key="i"
                                class="star"
                                src="@imgs/plantSelection/star.png"
                            />
                        </div>
                        <!-- 道具按钮 -->
                        <div
                            class="btn"
                            :class="[
                                !item.owned
                                    ? isGet(item.code)
                                        ? 'go-get'
                                        : 'lock-btn'
                                    : wearTitle.includes(item.propKind) &&
                                      !(item.propType == '标题装饰' && props.chooseKind == 0)
                                    ? item.actived
                                        ? 'on'
                                        : 'off'
                                    : 'off',
                                item.propType === '挂饰' && maxWear < 1 && item.owned ? 'lock-btn' : ''
                            ]"
                            @click="wear(item)"
                        >
                            {{
                                !item.owned
                                    ? isGet(item.code)
                                        ? state.springFestival.去獲取
                                        : state.package_.未获得
                                    : wearTitle.includes(item.propKind) &&
                                      !(item.propType == '标题装饰' && props.chooseKind == 0)
                                    ? item.actived
                                        ? state.package_.取消使用
                                        : state.package_.使用
                                    : state.package_.去查看
                            }}
                        </div>
                        <div v-if="!item.owned" class="lock">
                            <img class="lock-img" src="@imgs/package/lock.png" />
                        </div>
                    </template>
                    <template v-else>
                        <div class="tips">
                            <p>{{ state.联动奖赏.後付限定 }}</p>
                        </div>
                        <div class="newest" v-if="item.newest">
                            {{ state.联动奖赏.新 }}
                        </div>
                        <div class="name">
                            <p>{{ state.package.role[item.code] }}</p>
                        </div>
                        <div class="preview2" @click="showGifProp(item)">
                            <img :src="$imgs[`props/vip/${item.code}/1.png`]" alt="" />
                        </div>
                        <div class="value">
                            <img
                                v-for="i in Number(item.rarity)"
                                :key="i"
                                class="star"
                                src="@imgs/plantSelection/star.png"
                            />
                        </div>
                        <div
                            class="btn"
                            :class="!item.owned ? 'lock-btn' : item.actived ? 'on' : 'off'"
                            @click="wearVip(item)"
                        >
                            {{
                                !item.owned
                                    ? state.联动奖赏.未获取
                                    : item.actived
                                    ? state.package_.取消使用
                                    : state.package_.使用
                            }}
                        </div>
                        <div v-if="!item.owned" class="lock">
                            <img class="lock-img" src="@imgs/package/lock.png" />
                        </div>
                    </template>
                </div>
            </div>

            <!-- 功能道具展示区域（kindIndex == 1） -->
            <div v-if="kindIndex == 1" class="kindIndex">
                <!-- 我的功能卡分类（chooseKind == 0）- 暂时为空 -->
                <div class="funkin" v-if="chooseKind == 0"></div>
                <!-- 我的功能卡列表展示（chooseKind == 1 且有功能道具） -->
                <div class="funkin" v-if="chooseKind == 1 && list['功能道具'].length > 0">
                    <!-- 循环展示功能道具列表 -->
                    <div
                        @click="showCardInfo(item.type)"
                        class="item item1 item2"
                        v-for="(item, index) in list['功能道具']"
                        :key="index"
                    >
                        <!-- 道具信息图标 -->
                        <img class="info" :src="$imgs['note.png']" alt="" />
                        <!-- 道具过期时间显示 -->
                        <p class="num">{{ dayjs(item.destory_at).format('YYYY.MM.DD') }}{{ state.home.过期1 }}</p>
                        <!-- 道具名称（根据code动态显示不同卡片名称） -->
                        <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">
                            {{
                                item.code == 'x2wewalk'
                                    ? state.home.步数单日双倍卡
                                    : item.code == 'x2energy'
                                    ? state.home.减碳值单日双倍卡
                                    : item.code == 'energy20'
                                    ? state.联动奖赏.碳值卡20g
                                    : item.code == 'energy30'
                                    ? state.联动奖赏.碳值卡30g
                                    : item.code == 'energy10'
                                    ? state.联动奖赏.碳值卡10g
                                    : ''
                            }}
                        </div>
                        <!-- 道具预览图（根据code显示对应图片） -->
                        <div class="preview1">
                            <img
                                :src="
                                    $imgs[
                                        `${
                                            item.code == 'x2wewalk'
                                                ? 'Double_step'
                                                : item.code == 'x2energy'
                                                ? 'reduction_card'
                                                : item.code == 'energy20'
                                                ? '碳值20'
                                                : item.code == 'energy30'
                                                ? '碳值30'
                                                : item.code == 'energy10'
                                                ? '碳值10'
                                                : ''
                                        }.png`
                                    ]
                                "
                                alt=""
                            />
                        </div>
                        <div
                            @click.stop="useCardItem(item, index)"
                            :class="`btn ${
                                isUsingCard ? (item.code == 'x2wewalk' || item.code == 'x2energy' ? 'btnGray' : '') : ''
                            }`"
                        >
                            {{ state.package_.使用 }}
                        </div>
                    </div>
                </div>
                <div class="funkin" v-if="chooseKind == 1 && list.length == 0">
                    <div class="nullBox">
                        <img class="null" :src="$imgs['No_functional.png']" alt="" />
                        <p class="nullFont">{{ state.package_.你还没有双倍卡道具 }}</p>
                    </div>
                </div>
                <div class="funkin" v-if="chooseKind == -1">
                    <List
                        v-if="list['失效卡'].length > 0"
                        v-model:loading="loading"
                        :finished="finished"
                        :finished-text="state.联动奖赏.没有更多了"
                        :loading-text="state.联动奖赏.加载中"
                        @load="onLoad"
                    >
                        <!-- <div class="item item1 item2 item4" v-for="(item, index) in list['失效卡']" :key="index"> -->
                        <div class="item item1 item2 item4" v-for="(item, index) in listArr" :key="index">
                            <img
                                class="shixiao"
                                :src="item.actived ? $imgs[state.联动奖赏.已使用] : $imgs[state.联动奖赏.已过期]"
                                alt=""
                            />
                            <img class="info" :src="$imgs['note.png']" alt="" />
                            <!-- <p class="num">{{dayjs(item.destory_at).format('YYYY.MM.DD')}}{{state.home.过期1}}</p> -->
                            <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">
                                {{
                                    item.code == 'x2wewalk'
                                        ? state.home.步数单日双倍卡
                                        : item.code == 'x2energy'
                                        ? state.home.减碳值单日双倍卡
                                        : item.code == 'energy20'
                                        ? state.联动奖赏.碳值卡20g
                                        : item.code == 'energy30'
                                        ? state.联动奖赏.碳值卡30g
                                        : ''
                                }}
                            </div>
                            <div class="preview1">
                                <img
                                    :src="
                                        $imgs[
                                            `${
                                                item.code == 'x2wewalk'
                                                    ? 'Double_step'
                                                    : item.code == 'x2energy'
                                                    ? 'reduction_card'
                                                    : item.code == 'energy20'
                                                    ? '碳值20'
                                                    : item.code == 'energy30'
                                                    ? '碳值30'
                                                    : ''
                                            }.png`
                                        ]
                                    "
                                    alt=""
                                />
                            </div>
                            <!-- <div @click.stop="useCardItem(item, index)" :class="`btn ${isUsingCard ? 'btnGray' : ''}`">
                                {{state.package_.使用}}
                            </div> -->
                        </div>
                    </List>
                    <div v-if="list['失效卡'].length == 0" class="Null">
                        <img :src="$imgs['NullCard.png']" alt="" />
                        <p>{{ state.联动奖赏.暂无失效道具 }}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- 会员成长tab（tabIndex == 1） -->
        <div style="height: 100%" v-if="tabIndex == 1">
            <!-- 会员装饰道具展示区域（kindIndex == 0）- 在功能道具tab下显示会员成长道具 -->
            <div v-if="kindIndex == 0" class="kindIndex">
                <!-- 循环展示会员成长道具 -->
                <div v-for="(item, index) in list['会员成长']" :key="index" class="item item3">
                    <!-- 会员专属标签（部分道具显示） -->
                    <div class="tips" v-if="removeTips(item.code)">
                        <p>{{ state.联动奖赏.後付限定 }}</p>
                    </div>
                    <!-- 新道具标识 -->
                    <div class="newest" v-if="item.newest">
                        {{ state.联动奖赏.新 }}
                    </div>
                    <!-- 会员道具名称 -->
                    <div class="name">
                        <p>{{ state.package.role[item.code] }}</p>
                    </div>
                    <!-- 会员道具预览图（GIF动画） -->
                    <div class="preview2" @click="showGifProp(item)">
                        <img :src="$imgs[`props/vip/${item.code}/1.png`]" alt="" />
                    </div>
                    <!-- 道具稀有度星级显示 -->
                    <div class="value">
                        <img
                            v-for="i in Number(item.rarity)"
                            :key="i"
                            class="star"
                            src="@imgs/plantSelection/star.png"
                        />
                    </div>
                    <!-- 会员道具操作按钮 -->
                    <div
                        class="btn"
                        :class="!item.owned ? 'lock-btn' : item.actived ? 'on' : 'off'"
                        @click="wearVip(item)"
                    >
                        {{
                            !item.owned
                                ? state.联动奖赏.未获取
                                : item.actived
                                ? state.package_.取消使用
                                : state.package_.使用
                        }}
                    </div>
                    <!-- 未拥有道具的锁定图标 -->
                    <div v-if="!item.owned" class="lock">
                        <img class="lock-img" src="@imgs/package/lock.png" />
                    </div>
                </div>
                <!-- 预留：会员遮罩层（已注释） -->
                <!-- <div class="notVip" v-if="!useUserStore().isVip">
                    <div @click="beVip" class="beVip">
                        {{state.home.成为会员}}
                    </div>
                    <p class="beavip">{{state.home.成为会员解锁}}</p>
                </div> -->
            </div>
            <!-- 功能道具展示区域（kindIndex == 1）- 包含我的功能卡、双倍卡、碳值卡等 -->
            <div v-if="kindIndex == 1" class="kindIndex">
                <!-- 我的功能卡展示（chooseKind == 0）- 在功能道具tab下的功能卡分类 -->
                <div class="funkin" v-if="chooseKind == 0">
                    <div
                        @click="showCardInfo(item.type, item.use_type)"
                        class="item item1 item2"
                        v-for="(item, index) in list['功能道具']"
                        :key="index"
                    >
                        <!-- <img class="gantanhao" :src="$imgs['exclamation1.png']" alt=""> -->
                        <!-- <img class="info" :src="$imgs['note.png']" alt=""> -->
                        <!-- 会员道具标签 -->
                        <!-- 会员道具标签（use_type == 2 表示会员专属） -->
                        <div class="tips" v-if="item.use_type == 2">
                            <p>{{ state.联动奖赏.後付限定 }}</p>
                        </div>
                        <!-- 新道具标识 -->
                        <div class="newest" v-if="item.newest">
                            {{ state.联动奖赏.新 }}
                        </div>
                        <!-- 道具过期时间显示 -->
                        <p class="num">{{ dayjs(item.destory_at).format('YYYY.MM.DD') }}{{ state.home.过期1 }}</p>
                        <!-- 功能道具名称（根据code动态显示不同卡片名称） -->
                        <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">
                            <img v-if="item.code == 'energy30'" :src="$imgs['dialog/linkMember-icon1.png']" />
                            {{
                                item.code == 'x2wewalk'
                                    ? state.home.步数单日双倍卡
                                    : item.code == 'x2energy'
                                    ? state.home.减碳值单日双倍卡
                                    : item.code == 'energy20'
                                    ? state.联动奖赏.碳值卡20g
                                    : item.code == 'energy30'
                                    ? state.联动奖赏.碳值卡30g
                                    : item.code == 'energy10'
                                    ? state.联动奖赏.碳值卡10g
                                    : ''
                            }}
                        </div>
                        <!-- 功能道具预览图（根据code显示对应图片） -->
                        <div class="preview1">
                            <img
                                :src="
                                    $imgs[
                                        `${
                                            item.code == 'x2wewalk'
                                                ? 'Double_step'
                                                : item.code == 'x2energy'
                                                ? 'reduction_card'
                                                : item.code == 'energy20'
                                                ? '碳值20'
                                                : item.code == 'energy30'
                                                ? '碳值30'
                                                : item.code == 'energy10'
                                                ? '碳值10'
                                                : ''
                                        }.png`
                                    ]
                                "
                                alt=""
                            />
                        </div>
                        <!-- 使用道具按钮（双倍卡使用中时变灰色） -->
                        <div
                            @click.stop="useCardItem(item, index)"
                            :class="`btn ${
                                isUsingCard ? (item.code == 'x2wewalk' || item.code == 'x2energy' ? 'btnGray' : '') : ''
                            }`"
                        >
                            {{ state.package_.使用 }}
                        </div>
                    </div>
                    <!-- 预留：无功能道具时的占位显示（已注释） -->
                    <!-- <div @click="showCardInfo(index)" class="item item1 item2" v-if="list['功能道具'].length == 0" v-for="(item,index) in 2">
                        <div class="gray">
                            <img class="lock" src="@imgs/package/lock.png" />
                        </div>
                        <p class="num"> {{state.home.目前数量}} <span>0{{ state.package_.张}}</span> </p>
                        <img class="info" :src="$imgs['note.png']" alt="">
                        <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">{{index == 0?state.home.步数单日双倍卡:state.home.减碳值单日双倍卡 }}</div>
                        <div class="preview1">
                            <img :src="$imgs[`${
                                index == 0 ? 'Double_step' : 'reduction_card'
                            }.png`]" alt="">
                        </div>
                        <div @click.stop="useCard(1)" v-if="index == 0?energy20 ==0 : energy30==0" :class="`btn ${isUsingCard && props.cardNum[1] > 0 ? 'btnGray' : ''}`">
                            {{props.cardNum?.[1] > 0 ? state.package_.使用 : state.package_.去获取}}
                        </div>
                    </div> -->
                    <!-- 预留：无卡片时的空状态显示（已注释） -->
                    <!-- <div class="cardNull" v-if="list['功能道具'].length == 0">
                        <img :src="$imgs['cardNull.png']" alt="">
                        <p v-html="state.联动奖赏.每月七号"></p>
                    </div> -->
                </div>
                <!-- 双倍卡分类展示（chooseKind == 1） -->
                <div class="funkin" v-else-if="chooseKind == 1">
                    <!-- 步数双倍卡 - 无卡片时的占位显示 -->
                    <div @click="showCardInfo(0)" :class="`item item1 item2`" v-if="x2wewalk == 0">
                        <!-- 灰色遮罩层 -->
                        <div class="gray">
                            <img class="lock" src="@imgs/package/lock.png" />
                        </div>
                        <!-- 显示当前数量为0 -->
                        <p class="num">
                            {{ state.home.目前数量 }}
                            <span>0{{ state.package_.张 }}</span>
                        </p>
                        <!-- 道具信息图标 -->
                        <img class="info" :src="$imgs['note.png']" alt="" />
                        <!-- 步数双倍卡名称 -->
                        <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">{{ state.home.步数单日双倍卡 }}</div>
                        <!-- 步数双倍卡预览图 -->
                        <div class="preview1">
                            <img :src="$imgs[`Double_step.png`]" alt="" />
                        </div>
                        <!-- 去获取按钮 -->
                        <div @click.stop="useCard(0, state.home.步数单日双倍卡)" :class="`btn`">
                            {{ state.package_.去获取 }}
                        </div>
                    </div>
                    <!-- 减碳值双倍卡 - 无卡片时的占位显示 -->
                    <div @click="showCardInfo(1)" :class="`item item1 item2`" v-if="x2energy == 0">
                        <!-- 灰色遮罩层 -->
                        <div class="gray">
                            <img class="lock" src="@imgs/package/lock.png" />
                        </div>
                        <!-- 显示当前数量为0 -->
                        <p class="num">
                            {{ state.home.目前数量 }}
                            <span>0{{ state.package_.张 }}</span>
                        </p>
                        <!-- 道具信息图标 -->
                        <img class="info" :src="$imgs['note.png']" alt="" />
                        <!-- 减碳值双倍卡名称 -->
                        <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">{{ state.home.减碳值单日双倍卡 }}</div>
                        <!-- 减碳值双倍卡预览图 -->
                        <div class="preview1">
                            <img :src="$imgs[`reduction_card.png`]" alt="" />
                        </div>
                        <!-- 去获取按钮 -->
                        <div @click.stop="useCard(1, state.home.减碳值单日双倍卡)" :class="`btn`">
                            {{ state.package_.去获取 }}
                        </div>
                    </div>
                    <!-- 循环展示双倍卡（步数双倍卡和减碳值双倍卡） -->
                    <div
                        @click="showCardInfo(index)"
                        v-for="(item, index) in ['x2wewalk', 'x2energy']"
                        :key="index"
                        class="item3"
                    >
                        <!-- 当对应双倍卡数量大于0时显示 -->
                        <div v-if="index == 0 ? x2wewalk > 0 : x2energy > 0" class="item item1 item2">
                            <!-- 显示当前拥有的卡片数量 -->
                            <p class="num1">
                                {{ state.home.目前数量 }} :
                                <span class="num1-length">
                                    {{ item == 'x2wewalk' ? x2wewalk : item == 'x2energy' ? x2energy : '0'
                                    }}{{ state.package_.张 }}
                                </span>
                            </p>
                            <!-- 双倍卡名称（根据类型显示不同名称） -->
                            <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">
                                {{
                                    item == 'x2wewalk'
                                        ? state.home.步数单日双倍卡
                                        : item == 'x2energy'
                                        ? state.home.减碳值单日双倍卡
                                        : ''
                                }}
                            </div>
                            <!-- 双倍卡预览图 -->
                            <div class="preview1">
                                <img
                                    :src="
                                        $imgs[
                                            `${
                                                item == 'x2wewalk'
                                                    ? 'Double_step'
                                                    : item == 'x2energy'
                                                    ? 'reduction_card'
                                                    : ''
                                            }.png`
                                        ]
                                    "
                                    alt=""
                                />
                            </div>
                            <div @click.stop="toMyCard" :class="`btn`">
                                {{ state.package_.使用 }}
                            </div>
                        </div>
                    </div>

                    <!-- <div class="cardNull" v-if="list['双倍卡'].length == 0">
                        <img :src="$imgs['cardNull.png']" alt="">
                        <p v-html="state.联动奖赏.每月七号"></p>
                    </div> -->
                </div>
                <!-- 碳值卡分类展示（chooseKind == 2） -->
                <div class="funkin" v-else-if="chooseKind == 2">
                    <!-- 往期代码，先注释掉。循环展示20g和30g碳值卡（使用template循环2次） -->
                    <!-- <template v-if="false" v-for="(item, index) in 2">
                        <div
                            class="item item1 item2"
                            @click="showCardInfo(index + 2)"
                            v-if="index == 0 ? energy20 > 0 : energy30 > 0"
                        >
                            <div class="tips">
                                <p>{{ state.联动奖赏.後付限定 }}</p>
                            </div>
                            <p class="num1">
                                {{ state.home.目前数量 }} :
                                <span class="num1-length">
                                    {{ index == 0 ? energy20 : energy30 }}{{ state.package_.张 }}
                                </span>
                            </p>
                            <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">
                                {{ index == 0 ? state.联动奖赏.碳值卡20g : state.联动奖赏.碳值卡30g }}
                            </div>
                            <div class="preview1">
                                <img :src="$imgs[`碳值20.png`]" alt="" />
                            </div>
                            <div class="btn2 btn" v-if="index == 0 ? energy20 == 0 : energy30 == 0">
                                {{ state.package_.未获得 }}
                            </div>
                            <div @click.stop="toMyCard" :class="`btn`" v-else>
                                {{ state.package_.使用 }}
                            </div>
                            <div class="lock" v-if="index == 0 ? energy20 == 0 : energy30 == 0">
                                <img class="lock-img" src="@imgs/package/lock.png" />
                            </div>
                        </div>
                    </template> -->
                    <!-- 10g碳值卡：会员：且有10g就显示，没有则隐藏 ; 后付+非会员：显示10g -->
                    <div
                        class="item item1 item2"
                        v-if="energy10 > 0 || !useUserStore().amm_vip"
                        @click="showCardInfo(4)"
                    >
                        <!-- 会员专属标签 -->
                        <div class="tips">
                            <p>{{ state.联动奖赏.後付限定 }}</p>
                        </div>
                        <!-- 显示当前拥有的10g碳值卡数量 -->
                        <p class="num1">
                            {{ state.home.目前数量 }} :
                            <span class="num1-length">{{ energy10 }}{{ state.package_.张 }}</span>
                        </p>
                        <!-- 10g碳值卡名称 -->
                        <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">
                            {{ state.联动奖赏.碳值卡10g }}
                        </div>
                        <!-- 10g碳值卡预览图 -->
                        <div class="preview1">
                            <img :src="$imgs[`碳值10.png`]" alt="" />
                        </div>
                        <!-- 无卡片时的详情按钮 -->
                        <div v-if="energy10 == 0" :class="`btn btnGray`">
                            {{ state.package_.详情 }}
                        </div>
                        <!-- 有卡片时的使用按钮 -->
                        <div @click.stop="toMyCard" :class="`btn`" v-else>
                            {{ state.package_.使用 }}
                        </div>
                        <!-- 无卡片时的锁定图标 -->
                        <div class="lock" v-if="energy10 == 0">
                            <img class="lock-img" src="@imgs/package/lock.png" />
                        </div>
                    </div>
                    <!-- 后付+link会员：30g，没有则置灰，只要是后付 要一直显示 -->
                    <div class="item item1 item2" @click="showCardInfo(3)">
                        <!-- 会员专属标签 -->
                        <div class="tips">
                            <p>{{ state.联动奖赏.後付限定 }}</p>
                        </div>
                        <p class="num1">
                            {{ state.home.目前数量 }} :
                            <span class="num1-length">{{ energy30 }}{{ state.package_.张 }}</span>
                        </p>
                        <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">
                            <img :src="$imgs['dialog/linkMember-icon1.png']" />
                            <span>{{ state.联动奖赏.碳值卡30g }}</span>
                        </div>
                        <div class="preview1">
                            <img :src="$imgs[`碳值30.png`]" alt="" />
                        </div>
                        <div
                            v-if="energy30 == 0"
                            class="btn btnGray"
                            :class="{ purple: !useUserStore().amm_vip }"
                            @click.stop="showBuyLinkVip"
                        >
                            {{ useUserStore().amm_vip ? state.link会员.详情 : state.link会员.去获取 }}
                        </div>
                        <!-- 有卡片时的使用按钮 -->
                        <div @click.stop="toMyCard" :class="`btn`" v-else>
                            {{ state.package_.使用 }}
                        </div>
                        <!-- 无卡片时的锁定图标 -->
                        <div class="lock" v-if="energy30 == 0">
                            <img class="lock-img" src="@imgs/package/lock.png" />
                        </div>
                    </div>
                </div>
                <!--实效卡分类展示（chooseKind == -1） -->
                <div class="funkin" v-if="chooseKind == -1">
                    <List
                        v-if="list['失效卡'].length > 0"
                        v-model:loading="loading"
                        :finished="finished"
                        :finished-text="state.联动奖赏.没有更多了"
                        :loading-text="state.联动奖赏.加载中"
                        @load="onLoad"
                    >
                        <!-- <div class="item item1 item2 item4" v-for="(item, index) in list['失效卡']" :key="index"> -->
                        <div class="item item1 item2 item4" v-for="(item, index) in listArr" :key="index">
                            <img
                                class="shixiao"
                                :src="item.actived ? $imgs[state.联动奖赏.已使用] : $imgs[state.联动奖赏.已过期]"
                                alt=""
                            />
                            <img class="info" :src="$imgs['note.png']" alt="" />
                            <!-- <p class="num">{{dayjs(item.destory_at).format('YYYY.MM.DD')}}{{state.home.过期1}}</p> -->
                            <div :class="`name1 ${lang == 'en' ? 'name2' : ''}`">
                                {{
                                    item.code == 'x2wewalk'
                                        ? state.home.步数单日双倍卡
                                        : item.code == 'x2energy'
                                        ? state.home.减碳值单日双倍卡
                                        : item.code == 'energy20'
                                        ? state.联动奖赏.碳值卡20g
                                        : item.code == 'energy30'
                                        ? state.联动奖赏.碳值卡30g
                                        : item.code == 'energy10'
                                        ? state.联动奖赏.碳值卡10g
                                        : ''
                                }}
                            </div>
                            <div class="preview1">
                                <img
                                    :src="
                                        $imgs[
                                            `${
                                                item.code == 'x2wewalk'
                                                    ? 'Double_step'
                                                    : item.code == 'x2energy'
                                                    ? 'reduction_card'
                                                    : item.code == 'energy20'
                                                    ? '碳值20'
                                                    : item.code == 'energy30'
                                                    ? '碳值30'
                                                    : item.code == 'energy10'
                                                    ? '碳值10'
                                                    : ''
                                            }.png`
                                        ]
                                    "
                                    alt=""
                                />
                            </div>
                            <!-- <div @click.stop="useCardItem(item, index)" :class="`btn ${isUsingCard ? 'btnGray' : ''}`">
                                {{state.package_.使用}}
                            </div> -->
                        </div>
                    </List>
                    <div v-if="list['失效卡'].length == 0" class="Null">
                        <img :src="$imgs['NullCard.png']" alt="" />
                        <p>{{ state.联动奖赏.暂无失效道具 }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, onMounted, onBeforeMount, defineExpose } from 'vue'
import { useLang, useDialog, useToast, useDayjs, useEnvConfig, useLoading } from 'hook'
import obtainDialog from '@/components/obtainDialog.vue'
import { usePropStore, useUserStore } from '@/store'
import { Icon, List } from 'vant'
import { throttle } from '@/util/unity'
import PropDialog from '../propDialog.vue'
import useCardDialog from '../useCardDialog.vue'
import onlyOneDialog from '../onlyOneDialog.vue'
import gifPropDialog from '../gifPropDialog.vue'
import getLinkMemberDialog from '../getLinkMemberDialog.vue'

import { imgs } from '@assets/imgs'
import { logEventStatistics } from '@via/mylink-sdk'
import { emit, nextTick } from 'process'
import { useEventBus } from 'hook'

const { state, lang } = useLang()
const { wearProp } = usePropStore()
const { toast } = useToast()
const { dayjs } = useDayjs()

const emits = defineEmits(['init', 'toLog', 'updata:wearList', 'updata:chooseKind', 'updata:isUsingCard', 'toMyCard'])

const isGet = (code: string) => {
    const codeArr = ['title_fuguizhu', 'title_taohua', 'item22', 'item23', 'item24', 'item25', 'item26']
    return codeArr.includes(code)
}

const listArr = ref([])
const loading = ref(false)
const finished = ref(false)

const dialog = useDialog({
    PropDialog,
    useCardDialog,
    onlyOneDialog,
    gifPropDialog,
    obtainDialog,
    getLinkMemberDialog
})
const props = defineProps({
    kindIndex: {
        // 道具大分类索引 0:装饰类道具 1:功能类道具 2:其他道具
        type: Number,
        required: true
    },
    chooseKind: {
        // 功能道具子分类选择 0:我的功能卡 1:双倍卡 2:碳值卡 -1:失效卡
        type: Number,
        required: false
    },
    tabIndex: {
        // 当前激活的主tab 0:mygarden 1:会员成长 2:减碳商城
        type: Number,
        required: false
    },
    list: {
        // 道具列表数据对象，包含各种分类的道具数组
        type: Object,
        required: false
    },
    maxWear: {
        // 最大可穿戴道具数量，-1表示没有植物，0表示植物太小
        type: Number,
        required: true
    },
    wearList: {
        // 当前已穿戴的道具列表
        type: Array<any>,
        required: true
    },
    cardNum: {
        // 各类卡片的数量统计对象
        type: Object,
        required: false
    },
    isUsingCard: {
        // 是否正在使用卡片（防止重复点击）
        type: Boolean,
        required: false
    },
    curTabName: {
        // 当前选中的装饰道具子分类名称（如：挂饰、标题装饰等）
        type: String,
        required: true
    }
})
console.log(props.list)
const wearTitle = ['我的花园']
let isPat = useEnvConfig().RUN_ENV == 'production' || useEnvConfig().RUN_ENV == 'beta' ? true : false

onMounted(() => {
    document.querySelector('.propItems')._isScroller = true
})

// 去除新增道具包的tips
const removeTips = (code) => {
    let arr = ['master', 'sculptor', 'fairy']
    return !arr.find((val) => code == val)
}

let timer: any = null
let onLoad: any = () => {
    // 异步更新数据
    // setTimeout 仅做示例，真实场景中一般为 ajax 请求

    timer = setTimeout(() => {
        if (props.list['失效卡']) {
            for (let i = 0; i < 9; i++) {
                if (props.list['失效卡'][listArr.value.length]) {
                    listArr.value.push(props.list['失效卡'][listArr.value.length])
                } else {
                    break
                }
            }
        }
        // 加载状态结束
        loading.value = false
        // 数据全部加载完成
        if (listArr.value.length >= props.list['失效卡'].length) {
            finished.value = true
        }
    }, 1000)
}

// 跳转我的功能卡
const toMyCard = () => {
    emits('toMyCard')
}

watch(
    () => props.tabIndex,
    (newVal, oldVal) => {
        if (newVal != oldVal) {
            listArr.value = []
            loading.value = false
            finished.value = false
        }
    }
)

const energy30 = ref(0)
const energy20 = ref(0)
const energy10 = ref(0)
const x2wewalk = ref(0) //双倍卡的数量
const x2energy = ref(0) //双倍卡的数量
watch(
    props,
    () => {
        energy30.value = 0
        energy20.value = 0
        x2wewalk.value = 0
        x2energy.value = 0
        energy10.value = 0
        for (const item in props.list['碳值卡']) {
            if (props.list['碳值卡'][item].code == 'energy30') {
                energy30.value += 1
            } else if (props.list['碳值卡'][item].code == 'energy20') {
                energy20.value += 1
            } else if (props.list['碳值卡'][item].code == 'energy10') {
                energy10.value += 1
            }
        }
        for (const item in props.list['双倍卡']) {
            if (props.list['双倍卡'][item].code == 'x2wewalk') {
                x2wewalk.value += 1
            } else if (props.list['双倍卡'][item].code == 'x2energy') {
                x2energy.value += 1
            }
        }
    },
    { deep: true }
)

const cleanListArr = () => {
    clearTimeout(timer)
    listArr.value = []
}

function init() {}

//装饰使用接口  // 挂道具
const wear = throttle(trueWear)
function trueWear(item: any) {
    if (!item.owned) {
        if (item.jump_url) {
            if (item.code.includes('title')) {
                logEventStatistics('graden_newyear_go_buy')
            } else {
                logEventStatistics('graden_specialnft_go_buy')
            }
            location.href = item.jump_url
        }
        return
    }
    if (item.propKind == '会员成长' && props.kindIndex == 0 && props.tabIndex == 0 && props.chooseKind == 0) {
        useEventBus().emit('changeXiaolimao')
        return
    }

    // 在全部的tab跳转到标题装饰的tab
    if (item.propType == '标题装饰' && props.kindIndex == 0 && props.tabIndex == 0 && props.chooseKind == 0) {
        useEventBus().emit('changeTitle')
        return
    }

    // 通过排除掉所有的数字获取同一类物品，主要针对替换的道具
    let code = item.code
        .replace(/(\d)+/g, '')
        // 针对装饰标题的特殊处理，对_号后所有的字符删除
        .replace(/_[^_]*$/, '')
    if (code == 'item') {
        //没种植
        if (props.maxWear == -1) {
            toast(state.dialog.没有植物可以装饰哦)
            return
        }
        //锁了
        if (!item.owned) return

        //不能穿戴
        if (!wearTitle.includes(item.propKind)) {
            return
        }
        if (!props.maxWear) {
            toast(state.dialog.植物太小啦)
            return
        }
    }

    //取下
    if (item.actived) {
        if (!props.wearList.length) return
        let index = props.wearList.indexOf(item)
        props.wearList.splice(index, 1)
        wearProp(!item.actived, item.item_id)
        item.actived = !item.actived
    } else {
        if (code == 'item') {
            //挂起
            //还没满
            let propsHanging = props.wearList.filter((item) => item.code.replace(/(\d)+/g, '') == 'item')
            if (propsHanging.length < props.maxWear) {
                wearProp(!item.actived, item.item_id)
                item.actived = !item.actived
                props.wearList?.push(item)
            } else {
                //满了
                if (props.maxWear == 1) {
                    //最多1的时候替换领一个
                    //可替换
                    //被替换的改状态
                    let p: any = props.wearList?.find((item) => item.code.replace(/(\d)+/g, '') == 'item')
                    let index = props.wearList.indexOf(p)
                    props.wearList.splice(index, 1)
                    wearProp(!p.actived, p.item_id)
                    p.actived = !p.actived
                    props.wearList?.push(item)
                    wearProp(!item.actived, item.item_id)
                    item.actived = !item.actived
                } else {
                    toast(state.dialog.已经挂满啦)
                }
            }
        } else {
            let p: any = props.wearList?.find((item) => item.code.replace(/(\d)+/g, '').replace(/_[^_]*$/, '') == code)
            if (typeof p == 'undefined') {
                props.wearList?.push(item)
            } else {
                let index = props.wearList.indexOf(p)
                props.wearList.splice(index, 1)
                wearProp(!p.actived, p.item_id)
                p.actived = !p.actived
                props.wearList?.push(item)
            }
            wearProp(!item.actived, item.item_id)
            item.actived = !item.actived
        }
    }

    emits('updata:wearList', props.wearList)
    // 埋点
    if (item.code.indexOf('title') >= 0) {
        logEventStatistics('garden_title_decorate_use')
    }
}

// 装饰道具图片点击触发
function showProp(item: any) {
    if (item.propType == '标题装饰') {
        showGifProp(item)
    } else {
        dialog.get('PropDialog').show({
            isInfo: false,
            propInstance: Object.assign({}, item, { icon: imgs[`props/${item.propType}/${item.code}.png`] })
        })
    }
}

function showGifProp(item: any) {
    // if (!useUserStore().inLogin) return
    dialog.get('gifPropDialog').show({
        type: 1,
        isInfo: false,
        propInstance: Object.assign({}, item)
    })
}

//跳转vip链接
const beVip = () => {
    if (!useUserStore().inLogin) {
        emits('toLog')
        return
    }
    window.location.href = isPat ? member_url.pat : member_url.uat
}
let member_url = {
    uat: 'openurl-modal://https://*************/activity/member/#/memberApply/introduce?isScroll=1&lang=<<cmcchkhsh_cmplang>>',
    pat: 'openurl-modal://https://mylink.komect.com/activity/member/#/memberApply/introduce?isScroll=1&lang=<<cmcchkhsh_cmplang>>'
}

const wearVip = (item) => {
    if (!item.owned) {
        return
    }

    if (item.actived) {
        if (!props.wearList.length) return
        let index = props.wearList.indexOf(item)
        props.wearList.splice(index, 1)
        wearProp(!item.actived, item.item_id)
        item.actived = !item.actived
    } else {
        logEventStatistics('garden_member_decoration_gif_use')
        let p: any = props.wearList?.find((i) => {
            // i.propType == 'GIF套装'
            return (
                i.code == 'hiker' ||
                i.code == 'musician' ||
                i.code == 'arborist' ||
                i.code == 'magician' ||
                i.code == 'astronaut' ||
                i.code == 'fairy' ||
                i.code == 'master' ||
                i.code == 'sculptor' ||
                i.code == 'nauticalexplorer' ||
                i.code == 'candymaster' ||
                i.code == 'windchaser' ||
                i.code == 'springwalkers'
            )
        })
        if (typeof p == 'undefined') {
            props.wearList?.push(item)
        } else {
            let index = props.wearList.indexOf(p)
            props.wearList.splice(index, 1)
            wearProp(!p.actived, p.item_id)
            p.actived = !p.actived
            props.wearList?.push(item)
        }
        wearProp(!item.actived, item.item_id)
        item.actived = !item.actived
    }
    emits('updata:wearList', props.wearList)
}

let blindUrl = {
    uat: `openurl-modal://http://*************/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true`,
    beta: `openurl-modal://https://mylink.komect.com/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true&debug=true`,
    pat: `openurl-modal://https://mylink.komect.com/activity/myCity/#/blindBox?lang=<<cmcchkhsh_cmplang>>&forceHideNavigationBar=true`
}

async function useCard(index: Number, type: String) {
    if (index == 1) {
        window.location.href =
            useEnvConfig().RUN_ENV == 'production'
                ? blindUrl.pat
                : useEnvConfig().RUN_ENV == 'beta'
                ? blindUrl.beta
                : blindUrl.uat
        return
    }
    if (props.isUsingCard && props.cardNum?.[index] > 0) {
        dialog.get('onlyOneDialog').show({}, { maskClose: false })
        return
    }
    if (props.cardNum?.[index] > 0) {
        emits('updata:chooseKind', 1)
    } else {
        //去获取
        const boo = await goToLogin()
        if (!boo) return
        logEventStatistics('garden_allcard_explore_click')
        if (useUserStore().isHK == 0) {
            let obtainDialog = dialog.get('obtainDialog')
            obtainDialog.show({ type })
            obtainDialog.on('toPackage', () => {
                obtainDialog.close()
            })
            obtainDialog.show({ aword: type })
            return
        }
        switch (index) {
            case 0:
                // window.location.href = isPat ? blindUrl.pat : blindUrl.uat
                window.location.href =
                    useEnvConfig().RUN_ENV == 'production'
                        ? blindUrl.pat
                        : useEnvConfig().RUN_ENV == 'beta'
                        ? blindUrl.beta
                        : blindUrl.uat

                break
            case 1:
                window.location.href =
                    useEnvConfig().RUN_ENV == 'production'
                        ? blindUrl.pat
                        : useEnvConfig().RUN_ENV == 'beta'
                        ? blindUrl.beta
                        : blindUrl.uat
                break
        }
    }
}

const goToLogin = async () => {
    useLoading().loading('open')
    await useUserStore().login()
    if (useUserStore().inLogin) {
        await emits('init')
        useLoading().loading('close')
        return true
    } else {
        useLoading().loading('close')
        return false
    }
}

//使用功能卡
function useCardItem(item: any, index: Number) {
    if (props.isUsingCard && !(item.code == 'energy30' || item.code == 'energy20' || item.code == 'energy10')) {
        dialog.get('onlyOneDialog').show({}, { maskClose: false })
        return
    }
    if (props.tabIndex == 0) {
        if (item.type == 1) {
            logEventStatistics('garden_mycard_double_crexp_use')
        } else if (item.type == 0) {
            logEventStatistics('garden_mycard_double_wewalk_crexp_use')
        }
    }
    if (props.tabIndex == 1) {
        if (props.chooseKind == 0) {
            logEventStatistics('garden_member_itemcard_use')
        } else if (props.chooseKind == 1) {
            logEventStatistics('garden_member_doublecard_use')
        } else if (props.chooseKind == 2) {
            logEventStatistics('garden_member_crexpcard_use')
        }
    }
    let useCardDia = dialog.get('useCardDialog')
    useCardDia.on('successUse', () => {
        // 将卡变为已使用
        item.actived = true
        listArr.value.unshift(item)
        emits('updata:isUsingCard', index, item)
    })
    useCardDia.show({ type: 1, kind: item.type, item }, { maskClose: false })
}
// 功能道具使用
function showCardInfo(index: Number, type = 0) {
    switch (index) {
        case 0:
            // 判断是不是减碳卡弹窗
            if (type == 2) {
                dialog.get('useCardDialog').show({ type: 0, kind: 0, style: true }, { maskClose: false })
            } else {
                dialog.get('useCardDialog').show({ type: 0, kind: 0 }, { maskClose: false })
            }
            break
        case 1:
            dialog.get('useCardDialog').show({ type: 0, kind: 1 }, { maskClose: false })
            break
        case 2:
            dialog.get('useCardDialog').show({ type: 0, kind: 2, style: true }, { maskClose: false })
            break
        case 3:
            dialog.get('useCardDialog').show({ type: 0, kind: 3, style: true }, { maskClose: false })
            break
        case 4:
            dialog.get('useCardDialog').show({ type: 0, kind: 4, style: true }, { maskClose: false })
            break
    }
}

const showBuyLinkVip = () => {
  if (useUserStore().amm_vip) {
    console.log('待获取状态，打开道具弹窗');
    showCardInfo(3)
  } else {
    logEventStatistics('jetsomygarden_clicon')
    dialog.get('getLinkMemberDialog').show({ type: 0, kind: 4, style: true }, { maskClose: false })
  }
}

init()

defineExpose({
    cleanListArr
})
</script>

<style lang="less">
.propItems {
    padding: 0 0 28px 0px;
    width: 100vw;
    box-sizing: border-box;
    overflow-y: scroll;
    overflow-x: hidden;
    .kindIndex {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        position: relative;
        align-content: flex-start;
        padding: 0 35px;
        .notVip {
            z-index: 13;
            padding-bottom: 100px;
            bottom: 0px;
            position: fixed;
            width: 100%;
            height: 500px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.89) 49%, #ffffff 100%);
            border-radius: 0px 0px 0px 0px;
            display: flex;
            flex-direction: column-reverse;
            align-items: center;

            .beavip {
                max-width: 420px;
                // position: absolute;
                // left: 50%;
                // transform: translate(-50%, 0);
                // bottom: 194px;
                text-align: center;
                font-size: 28px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #4e5b7e;
            }
        }
        .nullBox {
            // width: 100%;
            height: 100%;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            display: flex;
            flex-direction: column;
            align-items: center;
            // justify-content: center;
            .null {
                margin-top: 120px;
                height: 192px;
            }
            .nullFont {
                margin-top: 50px;
                font-size: 28px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #4e5b7e;
                white-space: pre-wrap;
                text-align: center;
            }
        }
        .beVip {
            // position: absolute;
            // bottom: 100px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #ffffff;
            font-size: 36px;
            text-align: center;
            flex-shrink: 0;
            // left: 50%;
            // transform: translate(-50%, 0);
            margin-top: 28px;
            width: 430px;
            z-index: 12;
            height: 72px;
            background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
            box-shadow: 0px 8px 0px 2px rgba(252, 175, 40, 1), inset 0px 4px 0px 2px rgba(255, 242, 178, 1);
            border-radius: 48px 12px 48px 12px;
            line-height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-top: 5px;
        }
        .van-list {
            width: 100%;
            display: flex;
            // justify-content: c;
            flex-wrap: wrap;
            position: relative;
            box-sizing: content-box;
            padding-bottom: 50px;
        }
        .van-list__finished-text {
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            bottom: 0;
        }
        .van-list__loading {
            text-align: center;
            width: 100%;
        }
        // .van-list__placeholder{
        //     position: absolute;
        //     left: 50%;
        //     transform: translate(-50%, 0);
        //     bottom: 0;
        // }
        .funkin {
            width: 100%;
            height: 100%;
            display: flex;
            // justify-content: ;
            flex-wrap: wrap;
            // position: relative;
            // justify-content: flex-start;
            // align-content: flex-start;
            .Null {
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                display: flex;
                flex-direction: column;
                align-items: center;
                > img {
                    width: 311px;
                    height: 180px;
                }
                > p {
                    text-align: center;
                    width: 504px;
                    margin-top: 40px;
                    font-size: 28px;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #4e5b7e;
                }
            }
            .cardNull {
                margin-top: 100px;
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                display: flex;
                flex-direction: column;
                align-items: center;
                > img {
                    width: 311px;
                    height: 180px;
                }
                > p {
                    text-align: center;
                    width: 504px;
                    margin-top: 40px;
                    font-size: 28px;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #4e5b7e;
                }
                span {
                    display: inline-block;
                    color: #fb7655;
                }
            }
            .noticeBox {
                // width: 652px;
                height: 102px;
                background: #e4fbef;
                border-radius: 12px 12px 12px 12px;
                opacity: 1;
                display: flex;
                padding: 28px 16px;
                align-items: center;
                margin: 24px 45px 0 50px;
                img {
                    width: 32px;
                    height: 24px;
                    margin-right: 16px;
                }
                p {
                    font-size: 24px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    color: #67718b;
                }
            }
        }
        .item-hy {
            background: url('@imgs/package/background3.png') no-repeat center center !important;
            background-size: 100% 100% !important;
        }
        .item:not(:nth-child(3n)) {
            margin-right: calc(10% / 2);
        }
        .item3:not(:nth-child(3n)) {
            margin-right: calc(10% / 2);
        }
        .item {
            margin-top: 28px;
            flex-shrink: 0;
            // width: 180px;
            // height: 238px;
            width: 30%;
            height: 276px;
            background: url('@imgs/package/background2.png') no-repeat center center;
            background-size: 100% 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            border-radius: 56px 16px 16px 16px;
            position: relative;
            // margin-left: 38px;
            .num1 {
                width: 180px;
                font-size: 16px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 500;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #4e5b7e;
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                bottom: 70px;
                .num1-length {
                    font-size: 20px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    color: #3cbf48;
                }
            }
            .tips {
                width: 104px;
                height: 28px;
                background: linear-gradient(89deg, #ffe1bf 0%, #dbaa69 100%);
                border-radius: 0px 16px 0px 16px;
                opacity: 1;
                position: absolute;
                top: 0;
                left: 92px;
                z-index: 999;
                p {
                    margin-left: 12px;
                    height: 28px;
                    line-height: 28px;
                    font-size: 20px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    color: #965f12;
                }
            }
            .newest {
                z-index: 3;
                position: absolute;
                top: 0;
                left: 0;
                padding: 2px 16px;
                background: linear-gradient(180deg, #ffa29a 0%, #f74231 100%);
                border-radius: 24px 24px 24px 4px;
                border: 2px solid #ffffff;
                font-size: 20px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #ffffff;
                line-height: 28px;
            }
            .info {
                position: absolute;
                width: 20px;
                top: 15px;
                right: 12px;
            }
            &.item3 {
                background: url('@imgs/package/background3.png') no-repeat center center;
                background-size: 100% 100%;
                .lock-img {
                    top: 130px !important;
                }
                > .preview2 {
                    // transform: translate(0, -6px);
                    position: absolute;
                    display: flex;
                    bottom: 90px;
                    height: 120px;
                    // margin-bottom: 60px;
                    align-items: center;
                    img {
                        height: 120px;
                        width: 120px;
                    }
                }
                > .on {
                    border: none !important;
                    color: #609eff !important;
                    background: #f0f9ff;
                    // box-shadow: 0px 4px 0px 2px rgba(240,249,255,1);
                    border-radius: 20px 6px 20px 6px;
                }
            }
            &.item4 {
                background: url('@imgs/package/background4.png') no-repeat center center !important;
                background-size: 100% 100% !important;
                .name1 {
                    color: #7b7b7b !important;
                }
                .preview1 {
                    filter: grayscale(100%);
                }
                .shixiao {
                    position: absolute;
                    width: 100%;
                    bottom: 0;
                    right: 0;
                }
                .info {
                    filter: grayscale(100%);
                }
            }
            &.item2 {
                background: url('@imgs/package/background2.png') no-repeat center center;
                background-size: 100% 100%;
                .timeOut {
                    position: absolute;
                    font-size: 17px;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #ffffff;
                    white-space: nowrap;
                    top: 6px;
                    left: 50%;
                    transform: translate(-50%, 0);
                }
            }
            &.item1 {
                width: 196px;
                height: 276px;
                display: flex;
                flex-direction: column;
                align-items: center;
                // margin-left: 38px;
                position: relative;
                // justify-content: end;
                .gantanhao {
                    position: absolute;
                    width: 20px;
                    height: 20px;
                    right: 16px;
                    top: 10x;
                }
                .gray {
                    top: 7px;
                    position: absolute;
                    width: 180px;
                    height: 212px;
                    z-index: 1;
                    background: linear-gradient(180deg, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0.08) 100%);
                    border-radius: 52px 16px 16px 16px;
                    > .lock {
                        position: absolute;
                        top: 100px;
                        left: 50%;
                        transform: translate(-50%, 0);
                        width: 49px;
                        height: 52px;
                    }
                }
                .num {
                    font-size: 16px;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #4e5b7e;
                    position: absolute;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    left: 50%;
                    transform: translate(-50%, 0);
                    bottom: 62px;
                    white-space: nowrap;
                    > span {
                        vertical-align: middle;
                        line-height: 30px;
                        margin-left: 4px;
                        display: inline-block;
                        padding: 2px 8px;
                        background: rgba(78, 202, 84, 0.5);
                        border-radius: 16px 16px 16px 16px;
                    }
                }
                .name1 {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    white-space: pre-line;
                    position: relative;
                    width: 160px;
                    z-index: 1;
                    text-align: center;
                    font-size: 22px;
                    font-family: PingFang TC-Semibold, PingFang TC;
                    font-weight: 600;
                    color: #4e5b7e;
                    margin-top: 30px;
                    img {
                        width: 28px;
                        height: 28px;
                        margin-right: 4px;
                    }
                    &.name2 {
                        width: 150px;
                        font-size: 16px;
                        margin-top: 28px;
                    }
                }

                > .preview1 {
                    transform: translate(0, -6px);
                    position: absolute;
                    display: flex;
                    bottom: 90px;
                    height: 90px;
                    // margin-bottom: 60px;
                    align-items: center;
                    img {
                        height: 100px;
                        width: 100px;
                    }
                }
                > .name {
                    top: 25px;
                }
                > .btn {
                    position: absolute;
                    padding-top: 3px;
                    bottom: 15px;
                    color: #fff;
                    background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
                    box-shadow: 0px 4px 0px 2px rgba(252, 175, 40, 1), inset 0px 2px 0px 2px rgba(255, 242, 178, 1);
                    &.btnGray {
                        background: linear-gradient(180deg, #d3d3d3 0%, #848484 100%);
                        box-shadow: 0px 4px 0px 2px rgba(147, 147, 147, 1), inset 0px 2px 0px 2px rgba(234, 234, 234, 1);
                    }
                    &.purple {
                        background: linear-gradient(180deg, #9b7efe 0%, #7b5afc 100%);
                        box-shadow: 0px 4px 0px 2px #6844f4, inset 0px 2px 0px 2px #b39cff;
                    }
                }
            }

            .preview {
                position: absolute;
                display: flex;
                bottom: 95px;
                height: 90px;
                align-items: center;
                img {
                    max-height: 90px;
                    max-width: 73px;
                }
                &::after {
                    display: block;
                    content: '';
                    width: 100%;
                    height: 25%;
                    bottom: 0;
                    background: linear-gradient(to bottom, rgba(238, 247, 237, 0), rgba(238, 247, 237, 1));
                    position: absolute;
                }
                &::before {
                    display: block;
                    content: '';
                    width: 100%;
                    height: 25%;
                    top: 0;
                    background: linear-gradient(to top, rgba(238, 247, 237, 0), rgba(238, 247, 237, 1));
                    position: absolute;
                }
            }
            &:last-of-type {
                margin-right: 0px;
            }
            .name {
                width: 145px;
                height: 50px;
                z-index: 1;
                position: absolute;
                top: 25px;
                left: 50%;
                transform: translate(-50%, 0);
                display: flex;
                align-items: center;
                justify-content: center;
                > p {
                    text-align: center;
                    font-size: 21px;
                    line-height: 25px;
                    font-family: PingFang TC-Semibold, PingFang TC;
                    font-weight: 600;
                    color: #4e5b7e;
                    overflow: hidden; //溢出内容隐藏
                    text-overflow: ellipsis; //文本溢出部分用省略号表示
                    display: -webkit-box; //特别显示模式
                    -webkit-line-clamp: 2; //行数
                    line-clamp: 2;
                    -webkit-box-orient: vertical; //盒子中内容竖直排列
                }
            }
            .value {
                margin-top: 200px;
                height: 15px;
                position: relative;
                .star {
                    width: 22px;
                    height: 20px;
                    position: relative;
                    bottom: 62px;
                }
                .num1 {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .num1-length {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    font-size: 20px;
                    color: #3cbf48;
                }
            }
            .btn2 {
                background: linear-gradient(180deg, #d3d3d3 0%, #848484 100%) !important;
                box-shadow: 0px 4px 0px 2px #939393, inset 0px 2px 0px 2px #eaeaea !important;
            }
            .btn {
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                bottom: 15px;
                z-index: 11;
                font-size: 22px;
                font-family: PingFang SC-Bold, PingFang SC;
                font-weight: bold;
                line-height: 28px;
                max-width: 160px;
                padding-left: 40px;
                padding-right: 40px;
                height: 32px;
                border-radius: 20px 6px 20px 6px;
                display: flex;
                justify-content: center;
                box-sizing: border-box;
                white-space: nowrap;
                &.funbtn {
                    padding-top: 3px;
                    margin-top: 181px;
                    color: #fff;
                    background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
                    box-shadow: 0px 4px 0px 2px rgba(252, 175, 40, 1), inset 0px 2px 0px 2px rgba(255, 242, 178, 1);
                }
                &.on {
                    border: 3px solid #22992c;
                    color: #22992c;
                    padding-top: 1px;
                    height: 36px;
                }
                &.off {
                    font-weight: bold;
                    padding-top: 5px;
                    background: linear-gradient(180deg, #fddd3e 0%, #fbb629 100%);
                    box-shadow: 0px 4px 0px 2px #fcaf28, inset 0px 2px 0px 2px #fff2b2;
                    color: white;
                }
                &.lock-btn {
                    padding-top: 5px;
                    color: white;
                    background: linear-gradient(180deg, #d3d3d3 0%, #848484 100%);
                    box-shadow: 0px 4px 0px 2px #939393, inset 0px 2px 0px 2px #eaeaea;
                }
            }
            > .lock {
                pointer-events: none;
                width: 100%;
                height: 100%;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0.36) 0%, rgba(0, 0, 0, 0.08) 100%);
                border-radius: 56px 16px 16px 16px;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 2;
                .lock-img {
                    width: 48.5px;
                    height: 52.3px;
                    position: absolute;
                    top: 140px;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            }
        }
    }
}
.go-get {
    width: 144px;
    height: 28px;
    background: #3cbf48;
    box-shadow: 0px 4px 0px 2px #23af2d, inset 0px 2px 0px 2px #b1ffb3;
    border-radius: 20px 6px 20px 6px;
    font-family: PingFang SC, PingFang SC;
    font-weight: bold;
    font-size: 22px;
    color: #ffffff;
    line-height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
