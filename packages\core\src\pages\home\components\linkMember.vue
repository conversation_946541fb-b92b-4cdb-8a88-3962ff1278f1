<template>
    <div>
        <div class="link-member" @click="showProp">
            <div class="bubble-text" :class="{ 'bubble-text_en': lang == 'en' }" v-if="showBubble">
                <span>{{ state.link会员.点击看看Link着数会员专属权益 }}</span>
            </div>
            <van-swipe
                class="swipe-container"
                :autoplay="5000"
                :show-indicators="true"
                @change="onChange"
                indicator-color="#7438FF"
            >
                <van-swipe-item>
                    <div class="box">
                        <img v-if="showRedDot" class="new" :src="$imgs['new.png']">
                        <img :src="$imgs['line-member1.png']" class="swipe-image" />
                    </div>
                </van-swipe-item>
                <van-swipe-item>
                    <div class="box">
                        <img v-if="showRedDot" class="new" :src="$imgs['new.png']">
                        <img :src="$imgs['line-member1.png']" class="swipe-image" />
                    </div>
                </van-swipe-item>
            </van-swipe>

            <div class="member-text">{{ state.link会员.Link着数会员 }}</div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { useDialog, useDayjs, useLang } from 'hook'
import { useTaskStore, useTreeStore, useUserStore, useNFTStore } from '@/store'
import linkMemberDialog from '@/components/linkMemberDialog.vue'
import { logEventStatistics } from '@via/mylink-sdk';

const { state } = useLang()
const custom = ref(false)
const dialog = useDialog({
    linkMemberDialog
})
const { dayjs } = useDayjs()

// 显示弹窗
const showProp = () => {
    logEventStatistics('jetsomygarden_firstad')
    dialog.get('linkMemberDialog').show({})
    handleClick() // 点击后关闭小红点
}

const showRedDot = ref(false) // 是否显示小红点
const showBubble = ref(false) // 是否显示气泡
const startTimeShowRedDot = '2025-8-18 11:00:00' // TODO:确定好上线时间后改一下时间。 开始显示小红点的时间
const LINK_MEMBER_RED_DOT = 'new_dot' // 小红点状态的key
const LINK_MEMBER_BUBBLE = 'lk_bubble' // 气泡状态的key
let bubbleTimer = null // 气泡定时器

// 处理点击事件，隐藏小红点
const handleClick = async () => {
    if (showRedDot.value) {
        showRedDot.value = false
        // 更新服务器状态，标记用户已点击
        if (useUserStore().inLogin) {
            await useUserStore().setUpData(LINK_MEMBER_RED_DOT, '1')
        }
    }
}

// 关闭气泡
const closeBubble = async () => {
    showBubble.value = false
    // 清除定时器
    if (bubbleTimer) {
        clearTimeout(bubbleTimer)
        bubbleTimer = null
    }
    // 更新服务器状态，标记气泡已显示过
    if (useUserStore().inLogin) {
        await useUserStore().setUpData(LINK_MEMBER_BUBBLE, '1')
    }
}

// 检查是否应该显示气泡
const checkBubbleVisibility = async () => {
    const currentTime = dayjs()
    const startTime = dayjs(startTimeShowRedDot)

    // 如果当前时间还没到活动开始时间，不显示气泡
    if (currentTime.isBefore(startTime)) {
        showBubble.value = false
        return
    }

    // 检查用户是否已经看过气泡（服务器状态）
    const userInfo = useUserStore().userInfo
    const hasSeen = userInfo?.webui_manage_values?.[LINK_MEMBER_BUBBLE]

    if (hasSeen) {
        showBubble.value = false
        return
    }

    // 首次进入，显示气泡
    showBubble.value = true

    // 5秒后自动关闭气泡
    bubbleTimer = setTimeout(async () => {
        await closeBubble()
    }, 5000)
}

// 检查是否应该显示小红点
const checkRedDotVisibility = async () => {
    const currentTime = dayjs()
    const startTime = dayjs(startTimeShowRedDot)

    // 如果当前时间还没到活动开始时间，不显示小红点
    if (currentTime.isBefore(startTime)) {
        showRedDot.value = false
        return
    }

    // 检查用户是否已经点击过（服务器状态）
    const userInfo = useUserStore().userInfo
    const hasClicked = userInfo?.webui_manage_values?.[LINK_MEMBER_RED_DOT]

    if (hasClicked) {
        showRedDot.value = false
        return
    }

    // 检查是否已经超过7天（从活动开始时间算起）
    const sevenDaysLater = startTime.add(7, 'day')
    if (currentTime.isAfter(sevenDaysLater)) {
        // 超过7天，自动隐藏小红点
        showRedDot.value = false
        // 更新服务器状态，标记为已过期
        if (useUserStore().inLogin) {
            await useUserStore().setUpData(LINK_MEMBER_RED_DOT, '1')
        }
    } else {
        // 还在7天内，显示小红点
        showRedDot.value = true
    }
}

// 监听登录状态和用户信息变化
watch(
    () => [useUserStore().inLogin, useUserStore().userInfo?.webui_manage_values],
    async ([isLogin, webuiManageValues]) => {
        if (isLogin && webuiManageValues) {
            await checkRedDotVisibility() // 检查小红点显示
            await checkBubbleVisibility() // 检查气泡显示
        } else {
            // 未登录或用户信息未加载时隐藏小红点和气泡
            showRedDot.value = false
            showBubble.value = false
            // 清除气泡定时器
            if (bubbleTimer) {
                clearTimeout(bubbleTimer)
                bubbleTimer = null
            }
        }
    },
    { immediate: true, deep: true }
)

// 组件销毁时清理定时器
onBeforeUnmount(() => {
    if (bubbleTimer) {
        clearTimeout(bubbleTimer)
        bubbleTimer = null
    }
})
</script>

<style scoped lang="less">
.link-member {
    position: relative;
    width: 100%;
    .bubble-text {
        position: absolute;
        background: #ffffff;
        border-radius: 28px 28px 28px 28px;
        bottom: 110px;
        left: -50px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 20px;
        width: 200px;
        z-index: 32;
        opacity: 0.9;
        span {
            font-size: 20px;
            text-align: center;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #4e5b7e;
            line-height: 30px;
        }
    }
    .bubble-text::after {
        position: absolute;
        left: 50%;
        top: 100%;
        width: 30px;
        height: 20px;
        content: '';
        clear: both;
        display: block;
        background: url('@assets/imgs/all-jt.png') no-repeat;
        background-size: cover;
        margin-top: -4px;
    }
    .bubble-text_en {
        max-width: 300px !important;
        left: -252px !important;
    }
    .bubble-text_en::after {
        left: 50% !important;
        transform: translateX(-50%) rotate(90deg);
    }
}

.swipe-container {
    width: 85px;
    height: 85px;
    border-radius: 8px;
    overflow: hidden;
    margin: 0 auto;
    .box {
        position: relative;
        .new {
            width: 36px;
            height: auto;
            position: absolute;
            top: 5px;
            left: 15px;
        }
    }
    // 自定义指示器样式
    :deep(.van-swipe__indicator) {
        width: 8px;
        height: 8px;
        background-color: #cccccc;
        opacity: 1;

        &.van-swipe__indicator--active {
            background-color: #7438ff;
        }
    }

    // 调整指示器位置
    :deep(.van-swipe__indicators) {
        bottom: 15px;
    }
}

.swipe-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.member-text {
    font-size: 18px;
    color: #830da7;
    height: 32px;
    padding: 0 12px;
    line-height: 32px;
    text-align: center;
    border-radius: 20px;
    margin-top: -12px;
    background: linear-gradient(94deg, #fffdff 0%, #f0dbff 100%);
    box-shadow: 0px 0 16px 2px rgba(2, 172, 159, 0.16), inset 0px -2px 0px 2px rgba(162, 28, 153, 0.3);
}
.link-member {
    position: relative;
    width: 100%;
}

.carousel-container {
    position: relative;
    width: 88px;
    height: 88px;
    overflow: hidden;
    border-radius: 8px;
    margin: 0 auto;
}

.carousel-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease-in-out;
}

.carousel-item {
    flex: 0 0 50%;
    height: 100%;
}

.carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #cccccc;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &.active {
        background-color: #7438ff;
    }
}
</style>
